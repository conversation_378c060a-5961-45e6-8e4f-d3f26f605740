<ion-content [fullscreen]="true">
  @if(checkedOrderIds.length == 0){
    <ion-fab horizontal="end" vertical="bottom" slot="fixed">
      <div class="fab-container">
        <ion-label class="fab-label" [ngClass]="{'ltr-label': isLtr, 'rtl-label': !isLtr}" i18n>Select All</ion-label>
        <ion-fab-button size="small" (click)="selectAllOrders()" >
          <ion-icon style="color:white;" src="/assets/icon/tasks-all-svgrepo-com.svg"></ion-icon>
        </ion-fab-button>
      </div>
    </ion-fab>
  }
  @if(checkedOrderIds.length > 0){
    <ion-fab horizontal="end" vertical="bottom" slot="fixed">
      <ion-fab-button>
        <ion-icon name="layers-outline"></ion-icon>
      </ion-fab-button>
      
      <ion-fab-list side="top">
    
        <div class="fab-container">
          <ion-label class="fab-label" [ngClass]="{'ltr-label': isLtr, 'rtl-label': !isLtr}" i18n>Change Status</ion-label>
          <ion-fab-button size="small" (click)="showAlertDeliveryOrStoreStatus()">
            <ion-icon name="repeat-outline"></ion-icon>
          </ion-fab-button>
        </div>
        <div class="fab-container">
          <ion-label class="fab-label" [ngClass]="{'ltr-label': isLtr, 'rtl-label': !isLtr}" i18n>Send To Delivery</ion-label>
          <ion-fab-button size="small" (click)="sendOrdersToDeliveryCompany()">
            <ion-icon src="assets/icon/driver-white.svg"></ion-icon>
          </ion-fab-button>
        </div>
      </ion-fab-list>
    </ion-fab>
  }
  <ion-refresher
    mode="md"
    slot="fixed"
    (ionRefresh)="handleRefresh($any($event))"
  >
    <ion-refresher-content
      refreshingSpinner="circles"
      i18n-refreshingText refreshingText="LOADING"
      style="text-align: center"
    >
    </ion-refresher-content>
  </ion-refresher>
  <app-user-header [resellerInfoData]="resellerInfo" [role]="role">
  </app-user-header>
  <ion-grid>
    
    <ion-row class="header-content">
      <h2 class="count-container">
        <ng-container i18n>Orders</ng-container> 
        <ion-label class="text-secondary"*ngIf="!(loadingListener$ | async)"> # {{ totalOrdersCount }}</ion-label>
        <ng-container *ngIf="(loadingListener$ | async)">
          <ion-spinner class="count-loading" mode="ios" color="primary"></ion-spinner>
        </ng-container>
      </h2>
      @if(filterApplied){
        <ion-button mode="ios" class="clearFilter" (click)="clearFilter()">
          <ion-icon src="/assets/icon/clearFilter.svg"></ion-icon>
          <ion-label>{{filterTitle}}</ion-label>
        </ion-button>
      }
    </ion-row>
    <ion-row class="alignment">
      <ion-col size="11">
        <ion-searchbar
          [debounce]="1000"
          (ionInput)="handleSearchInput($event)"
          mode="md"
          class="custom-searchbar"
          #seacrhInput
        ></ion-searchbar>
      </ion-col>
      <!-- <ion-col size="1" class="barcode-column">
        <ion-icon (click)="openBarcodeAction()" name="barcode-outline" role="button" class="barcode-icon"></ion-icon>
      </ion-col> -->
    </ion-row>
    <ion-toolbar>
      <ion-buttons slot="start">
          <ion-select 
          #ionSelectFilterStateEl 
          cancelText="Cancel" 
          mode="ios" 
          toggleIcon="caret-down-outline" 
          [value]="false" 
          (ionChange)="filterByStatus($event)" 
          interface="action-sheet" 
          [placeholder]="statusFilterPlaceHolder"
          color="'primary'"
          >
          
          <ion-select-option [value]="'all'">
            <span i18n>All</span>
          </ion-select-option>
            @for(status of  storeStatusOptionsList; track status.code){
              <ion-select-option [value]="status.code">
                {{status.name }}
              </ion-select-option>
            }
          
      </ion-select> 
      </ion-buttons>

      <ion-buttons slot="end">
        <!-- <ion-button (click)="openGroupingOrdersAlert()">
          <ion-icon name="filter-outline"></ion-icon>
        </ion-button> -->
        
        <ion-button style="--overflow:visible">
          @if(dateDomain.length>0){
            <ion-badge color="danger" class="overflow-badge">{{(selectedDateFilter?selectedDateFilter:'CUSTOM')}}</ion-badge>
          }
          <ion-icon [color]="dateDomain.length==0?'primary':'danger'" name="calendar-number-outline"></ion-icon>
          <ion-select (ionChange)="selectDates($event)" toggleIcon="false" mode="ios" style="padding: 0;max-width: 18px; position: absolute; inset-inline-end: 0; opacity: 0;" [(ngModel)]="selectedDateFilter" interface="popover">
            @for(date of dateFilter; track date.name){
              <ion-select-option  [value]="date.name">
                {{ date.name }}
              </ion-select-option>
            }
            @if(dateDomain.length>0){
              <ion-select-option color="danger" [value]="'CANCEL'">
                <span i18n>Cancel filter</span>
              </ion-select-option>
            }
          </ion-select>
        </ion-button>
      </ion-buttons>
      
    </ion-toolbar>
  <ng-container *ngIf="!(loadingListener$ | async)">
        @for (order of ordersItems | async; track order.id) {
          <app-order-card (onChangeSingleOrderStatus)="changeSingleOrderStatus($event,order)" [orderData]="order" [isChecked]="checkedOrders[order.id!] || false" (onCheckedChange)="onCheckedChange($event, order)" [isReseller]="true" (onEditOrder)="editOrder($event)"></app-order-card>
        }
    </ng-container>
  </ion-grid>

  <ng-container *ngIf="(loadingListener$ | async)">
    <ion-spinner class="page-loading" mode="ios" color="primary"></ion-spinner>
  </ng-container>
  <ion-infinite-scroll *ngIf="(canLoadMore$ | async)" threshold="100px" (ionInfinite)="onIonInfinite($event)">
    <ion-infinite-scroll-content
      style="--color: var(--ion-color-primary)"
      loadingSpinner="bubbles"
      i18n-loadingText loadingText="Loading More Orders..."
    ></ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
