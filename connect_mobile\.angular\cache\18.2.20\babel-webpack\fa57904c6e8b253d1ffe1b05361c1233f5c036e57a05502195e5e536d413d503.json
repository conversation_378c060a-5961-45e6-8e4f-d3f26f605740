{"ast": null, "code": "import _asyncToGenerator from \"D:/connect/connect_mobile/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _ResellerEditOrderModalComponent;\nimport { FormArray, FormGroup, FormControl, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { Modal<PERSON>ontroller, AlertController } from '@ionic/angular';\nimport { IonHeader, IonToolbar, IonTitle, IonButtons, IonButton, IonIcon, IonContent, IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonItem, IonLabel, IonInput, IonFooter, IonSpinner } from '@ionic/angular/standalone';\nimport { CommonModule } from '@angular/common';\nimport { updateResellerOrderHttpFail } from 'app/connect_modules/ngrx-stores/reseller/store/actions/reseller.actions';\nimport { Util } from 'app/connect_modules/shared/utils/util';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"app/connect_modules/reseller/services/reseller-orders-api.service\";\nimport * as i3 from \"@ngrx/store\";\nimport * as i4 from \"app/shared/services/util.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction ResellerEditOrderModalComponent_ion_card_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-card\", 9)(1, \"ion-card-header\")(2, \"ion-card-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ion-card-content\")(5, \"ion-item\")(6, \"ion-label\", 10);\n    i0.ɵɵtext(7, \"Product Variant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ion-input\", 11);\n    i0.ɵɵlistener(\"click\", function ResellerEditOrderModalComponent_ion_card_10_Template_ion_input_click_8_listener() {\n      const ctx_r1 = i0.ɵɵrestoreView(_r1);\n      const orderLineControl_r3 = ctx_r1.$implicit;\n      const i_r4 = ctx_r1.index;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.selectProduct(orderLineControl_r3, i_r4));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"ion-item\")(10, \"ion-label\", 10);\n    i0.ɵɵtext(11, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"ion-input\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"ion-item\")(14, \"ion-label\", 10);\n    i0.ɵɵtext(15, \"Price\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"ion-input\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"ion-item\")(18, \"ion-label\", 10);\n    i0.ɵɵtext(19, \"Total Price\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"ion-input\", 14);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const orderLineControl_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroupName\", i_r4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Product \", i_r4 + 1, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", ctx_r4.getProductVariantName((tmp_5_0 = orderLineControl_r3.get(\"product_variant\")) == null ? null : tmp_5_0.value));\n  }\n}\nfunction ResellerEditOrderModalComponent_ion_spinner_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-spinner\", 15);\n  }\n}\nfunction ResellerEditOrderModalComponent_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ResellerEditOrderModalComponent {\n  constructor(modalController, resellerOrdersApiService, store, alertController, utilService) {\n    this.modalController = modalController;\n    this.resellerOrdersApiService = resellerOrdersApiService;\n    this.store = store;\n    this.alertController = alertController;\n    this.utilService = utilService;\n    this.orderForm = new FormGroup({});\n    this.isSaving = false;\n    this.originalProductIds = [];\n    this.searchWarehouseVariant = searchTerm => {\n      let params = {\n        filters: [{\n          \"operator\": \"or\",\n          \"filters\": [{\n            \"field\": \"variant__name\",\n            \"operator\": \"contains\",\n            \"value\": searchTerm\n          }, {\n            \"field\": \"variant__sku\",\n            \"operator\": \"contains\",\n            \"value\": searchTerm\n          }]\n        }]\n      };\n      this.addProductIdFilter(params);\n      return this.resellerOrdersApiService.getResellerWarehouseVariants(params).toPromise().then(data => (data === null || data === void 0 ? void 0 : data.records) || []);\n    };\n  }\n  ngOnInit() {\n    this.initForm();\n    this.loadOrderData();\n    this.disableCalculatedFields();\n    this.storeOriginalProductIds();\n  }\n  initForm() {\n    this.orderForm = new FormGroup({\n      order_lines: new FormArray([])\n    });\n  }\n  storeOriginalProductIds() {\n    var _this$orderData;\n    if ((_this$orderData = this.orderData) !== null && _this$orderData !== void 0 && _this$orderData.order_lines) {\n      this.originalProductIds = this.orderData.order_lines.map(line => {\n        var _line$product_variant;\n        return (_line$product_variant = line.product_variant) === null || _line$product_variant === void 0 || (_line$product_variant = _line$product_variant.variant) === null || _line$product_variant === void 0 || (_line$product_variant = _line$product_variant.product) === null || _line$product_variant === void 0 ? void 0 : _line$product_variant.id;\n      }).filter(id => id);\n    }\n  }\n  loadOrderData() {\n    var _this$orderData2;\n    if (!((_this$orderData2 = this.orderData) !== null && _this$orderData2 !== void 0 && _this$orderData2.order_lines)) return;\n    const orderLinesArray = this.orderForm.get('order_lines');\n    this.orderData.order_lines.forEach(line => {\n      const lineGroup = new FormGroup({\n        id: new FormControl(line.id),\n        product_variant: new FormControl(line.product_variant, Validators.required),\n        quantity: new FormControl(line.quantity, [Validators.required, Validators.min(1)]),\n        price: new FormControl(line.price),\n        total_price: new FormControl(line.price * line.quantity),\n        variant_changed: new FormControl(false),\n        variant_change_reason: new FormControl('')\n      });\n      orderLinesArray.push(lineGroup);\n    });\n  }\n  get orderLines() {\n    return this.orderForm.get('order_lines');\n  }\n  disableCalculatedFields() {\n    this.orderLines.controls.forEach(control => {\n      var _control$get, _control$get2, _control$get3;\n      (_control$get = control.get('quantity')) === null || _control$get === void 0 || _control$get.disable();\n      (_control$get2 = control.get('price')) === null || _control$get2 === void 0 || _control$get2.disable();\n      (_control$get3 = control.get('total_price')) === null || _control$get3 === void 0 || _control$get3.disable();\n    });\n  }\n  addProductIdFilter(params) {\n    if (this.originalProductIds.length > 0) {\n      var _params$filters;\n      const productFilter = [{\n        \"operator\": \"and\",\n        \"filters\": [{\n          \"field\": \"variant__product__id\",\n          \"operator\": \"in\",\n          \"value\": this.originalProductIds.map(id => id.toString())\n        }]\n      }];\n      params.filters = Util.concatFilters((_params$filters = params.filters) !== null && _params$filters !== void 0 ? _params$filters : [], productFilter);\n    }\n  }\n  selectProduct(orderLineControl, index) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const searchTerm = '';\n        const variants = yield _this.searchWarehouseVariant(searchTerm);\n        const inputs = variants.map(variant => ({\n          name: 'variant',\n          type: 'radio',\n          label: variant.variant.name,\n          value: variant\n        }));\n        const alert = yield _this.alertController.create({\n          header: 'Select Product Variant',\n          inputs: inputs,\n          buttons: [{\n            text: 'Cancel',\n            role: 'cancel'\n          }, {\n            text: 'OK',\n            handler: selectedVariant => {\n              _this.handleChangeProduct(selectedVariant, orderLineControl, index);\n            }\n          }]\n        });\n        yield alert.present();\n      } catch (error) {\n        console.error('Error loading variants:', error);\n      }\n    })();\n  }\n  handleChangeProduct(event, orderLineControl, i) {\n    if (event) {\n      var _orderLineControl$get;\n      const quantity = ((_orderLineControl$get = orderLineControl.get('quantity')) === null || _orderLineControl$get === void 0 ? void 0 : _orderLineControl$get.value) || 1;\n      orderLineControl.patchValue({\n        product_variant: event,\n        price: event.variant.product.price,\n        total_price: event.variant.product.price * quantity,\n        variant_changed: true\n      });\n      console.log('Product changed:', event, 'for line:', i);\n    }\n  }\n  saveChanges() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.orderForm.valid) {\n        _this2.isSaving = true;\n        const orderLines = _this2.orderForm.get('order_lines');\n        const updatedOrderLines = orderLines.getRawValue().map(line => ({\n          id: line.id,\n          product_variant: typeof line.product_variant === 'object' ? line.product_variant.id : line.product_variant,\n          quantity: line.quantity,\n          price: line.price,\n          variant_changed: line.variant_changed || false,\n          variant_change_reason: line.variant_change_reason || ''\n        }));\n        const updateData = {\n          id: _this2.orderData.id,\n          order_lines: updatedOrderLines\n        };\n        console.log('Sending update data:', updateData);\n        try {\n          var _responseData$success, _responseData$fail_me;\n          const response = yield _this2.resellerOrdersApiService.updateResellerOrder(updateData).toPromise();\n          console.log('Update response:', response);\n          _this2.isSaving = false;\n          // Show success/fail messages using toast\n          const responseData = response;\n          if (responseData !== null && responseData !== void 0 && (_responseData$success = responseData.success_messages) !== null && _responseData$success !== void 0 && _responseData$success.length || responseData !== null && responseData !== void 0 && (_responseData$fail_me = responseData.fail_messages) !== null && _responseData$fail_me !== void 0 && _responseData$fail_me.length) {\n            var _responseData$success2, _responseData$fail_me2;\n            // Show success messages\n            if ((_responseData$success2 = responseData.success_messages) !== null && _responseData$success2 !== void 0 && _responseData$success2.length) {\n              const successMsg = responseData.success_messages.join('\\n');\n              _this2.utilService.presentToast(successMsg, 'success', 3000, 'top', 'Order Updated');\n            }\n            // Show fail messages\n            if ((_responseData$fail_me2 = responseData.fail_messages) !== null && _responseData$fail_me2 !== void 0 && _responseData$fail_me2.length) {\n              const failMsg = responseData.fail_messages.join('\\n');\n              _this2.utilService.presentToast(failMsg, 'danger', 5000, 'top', 'Update Issues');\n            }\n          }\n          _this2.modalController.dismiss(true);\n        } catch (error) {\n          console.error('Update error:', error);\n          _this2.isSaving = false;\n          _this2.utilService.presentToast('Failed to update order. Please try again.', 'danger', 3000, 'top', error.toString());\n          _this2.store.dispatch(updateResellerOrderHttpFail({\n            error\n          }));\n        }\n      }\n    })();\n  }\n  closeModal() {\n    this.modalController.dismiss(false);\n  }\n  getProductVariantName(productVariant) {\n    var _productVariant$varia;\n    if (!productVariant) return 'Select Product';\n    if (typeof productVariant === 'string') return productVariant;\n    return (productVariant === null || productVariant === void 0 || (_productVariant$varia = productVariant.variant) === null || _productVariant$varia === void 0 ? void 0 : _productVariant$varia.name) || (productVariant === null || productVariant === void 0 ? void 0 : productVariant.name) || 'Select Product';\n  }\n}\n_ResellerEditOrderModalComponent = ResellerEditOrderModalComponent;\n_ResellerEditOrderModalComponent.ɵfac = function ResellerEditOrderModalComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ResellerEditOrderModalComponent)(i0.ɵɵdirectiveInject(i1.ModalController), i0.ɵɵdirectiveInject(i2.ResellerOrdersApiService), i0.ɵɵdirectiveInject(i3.Store), i0.ɵɵdirectiveInject(i1.AlertController), i0.ɵɵdirectiveInject(i4.UtilService));\n};\n_ResellerEditOrderModalComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _ResellerEditOrderModalComponent,\n  selectors: [[\"app-reseller-edit-order-modal\"]],\n  inputs: {\n    orderData: \"orderData\"\n  },\n  standalone: true,\n  features: [i0.ɵɵProvidersFeature([ModalController, AlertController]), i0.ɵɵStandaloneFeature],\n  decls: 16,\n  vars: 5,\n  consts: [[\"slot\", \"end\"], [3, \"click\"], [\"name\", \"close\"], [3, \"formGroup\"], [\"formArrayName\", \"order_lines\"], [3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"expand\", \"block\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"name\", \"crescent\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"formGroupName\"], [\"position\", \"stacked\"], [\"formControlName\", \"product_variant\", \"readonly\", \"\", 3, \"click\", \"value\"], [\"formControlName\", \"quantity\", \"type\", \"number\", \"readonly\", \"\"], [\"formControlName\", \"price\", \"type\", \"number\", \"readonly\", \"\"], [\"formControlName\", \"total_price\", \"type\", \"number\", \"readonly\", \"\"], [\"name\", \"crescent\"]],\n  template: function ResellerEditOrderModalComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\")(1, \"ion-toolbar\")(2, \"ion-title\");\n      i0.ɵɵtext(3, \"Edit Order\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-buttons\", 0)(5, \"ion-button\", 1);\n      i0.ɵɵlistener(\"click\", function ResellerEditOrderModalComponent_Template_ion_button_click_5_listener() {\n        return ctx.closeModal();\n      });\n      i0.ɵɵelement(6, \"ion-icon\", 2);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(7, \"ion-content\")(8, \"form\", 3)(9, \"div\", 4);\n      i0.ɵɵtemplate(10, ResellerEditOrderModalComponent_ion_card_10_Template, 21, 3, \"ion-card\", 5);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(11, \"ion-footer\")(12, \"ion-toolbar\")(13, \"ion-button\", 6);\n      i0.ɵɵlistener(\"click\", function ResellerEditOrderModalComponent_Template_ion_button_click_13_listener() {\n        return ctx.saveChanges();\n      });\n      i0.ɵɵtemplate(14, ResellerEditOrderModalComponent_ion_spinner_14_Template, 1, 0, \"ion-spinner\", 7)(15, ResellerEditOrderModalComponent_span_15_Template, 2, 0, \"span\", 8);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"formGroup\", ctx.orderForm);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.orderLines.controls);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"disabled\", ctx.isSaving || !ctx.orderForm.valid);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isSaving);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isSaving);\n    }\n  },\n  dependencies: [CommonModule, i5.NgForOf, i5.NgIf, ReactiveFormsModule, i6.ɵNgNoValidate, i6.NgControlStatus, i6.NgControlStatusGroup, i6.FormGroupDirective, i6.FormControlName, i6.FormGroupName, i6.FormArrayName, IonHeader, IonToolbar, IonTitle, IonButtons, IonButton, IonIcon, IonContent, IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonItem, IonLabel, IonInput, IonFooter, IonSpinner],\n  styles: [\"ion-card[_ngcontent-%COMP%] {\\n  margin: 16px;\\n}\\n\\nion-item[_ngcontent-%COMP%] {\\n  --padding-start: 0;\\n  --inner-padding-end: 0;\\n}\\n\\nion-input[readonly][_ngcontent-%COMP%] {\\n  --color: var(--ion-color-medium);\\n}\\n\\nion-button[disabled][_ngcontent-%COMP%] {\\n  --opacity: 0.5;\\n}\\n\\nion-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcmVzZWxsZXIvcmVzZWxsZXItZWRpdC1vcmRlci1tb2RhbC9yZXNlbGxlci1lZGl0LW9yZGVyLW1vZGFsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsWUFBQTtBQUNGOztBQUVBO0VBQ0Usa0JBQUE7RUFDQSxzQkFBQTtBQUNGOztBQUVBO0VBQ0UsZ0NBQUE7QUFDRjs7QUFFQTtFQUNFLGNBQUE7QUFDRjs7QUFFQTtFQUNFLGlCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyJpb24tY2FyZCB7XG4gIG1hcmdpbjogMTZweDtcbn1cblxuaW9uLWl0ZW0ge1xuICAtLXBhZGRpbmctc3RhcnQ6IDA7XG4gIC0taW5uZXItcGFkZGluZy1lbmQ6IDA7XG59XG5cbmlvbi1pbnB1dFtyZWFkb25seV0ge1xuICAtLWNvbG9yOiB2YXIoLS1pb24tY29sb3ItbWVkaXVtKTtcbn1cblxuaW9uLWJ1dHRvbltkaXNhYmxlZF0ge1xuICAtLW9wYWNpdHk6IDAuNTtcbn1cblxuaW9uLXNwaW5uZXIge1xuICBtYXJnaW4tcmlnaHQ6IDhweDtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n});", "map": {"version": 3, "names": ["FormArray", "FormGroup", "FormControl", "Validators", "ReactiveFormsModule", "ModalController", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IonHeader", "IonToolbar", "IonTitle", "IonButtons", "IonButton", "IonIcon", "IonContent", "IonCard", "IonCardHeader", "IonCardTitle", "IonCardContent", "IonItem", "IonLabel", "IonInput", "<PERSON><PERSON><PERSON><PERSON>", "Ion<PERSON><PERSON><PERSON>", "CommonModule", "updateResellerOrderHttpFail", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ResellerEditOrderModalComponent_ion_card_10_Template_ion_input_click_8_listener", "ctx_r1", "ɵɵrestoreView", "_r1", "orderLineControl_r3", "$implicit", "i_r4", "index", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "selectProduct", "ɵɵelement", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate1", "getProductVariantName", "tmp_5_0", "get", "value", "ResellerEditOrderModalComponent", "constructor", "modalController", "resellerOrdersApiService", "store", "alertController", "utilService", "orderForm", "isSaving", "originalProductIds", "searchWarehouseVariant", "searchTerm", "params", "filters", "addProductIdFilter", "getResellerWarehouseVariants", "to<PERSON>romise", "then", "data", "records", "ngOnInit", "initForm", "loadOrderData", "disableCalculated<PERSON>ields", "storeOriginalProductIds", "order_lines", "_this$orderData", "orderData", "map", "line", "_line$product_variant", "product_variant", "variant", "product", "id", "filter", "_this$orderData2", "orderLinesArray", "for<PERSON>ach", "lineGroup", "required", "quantity", "min", "price", "total_price", "variant_changed", "variant_change_reason", "push", "orderLines", "controls", "control", "_control$get", "_control$get2", "_control$get3", "disable", "length", "_params$filters", "productFilter", "toString", "concatFilters", "orderLineControl", "_this", "_asyncToGenerator", "variants", "inputs", "name", "type", "label", "alert", "create", "header", "buttons", "text", "role", "handler", "<PERSON><PERSON><PERSON><PERSON>", "handleChangeProduct", "present", "error", "console", "event", "i", "_orderLineControl$get", "patchValue", "log", "saveChanges", "_this2", "valid", "updatedOrderLines", "getRawValue", "updateData", "_responseData$success", "_responseData$fail_me", "response", "updateResellerOrder", "responseData", "success_messages", "fail_messages", "_responseData$success2", "_responseData$fail_me2", "successMsg", "join", "presentToast", "failMsg", "dismiss", "dispatch", "closeModal", "productVariant", "_productVariant$varia", "ɵɵdirectiveInject", "i1", "i2", "ResellerOrdersApiService", "i3", "Store", "i4", "UtilService", "selectors", "standalone", "features", "ɵɵProvidersFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ResellerEditOrderModalComponent_Template", "rf", "ctx", "ResellerEditOrderModalComponent_Template_ion_button_click_5_listener", "ɵɵtemplate", "ResellerEditOrderModalComponent_ion_card_10_Template", "ResellerEditOrderModalComponent_Template_ion_button_click_13_listener", "ResellerEditOrderModalComponent_ion_spinner_14_Template", "ResellerEditOrderModalComponent_span_15_Template", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i6", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "FormGroupName", "FormArrayName", "styles"], "sources": ["D:\\connect\\connect_mobile\\src\\app\\reseller\\reseller-edit-order-modal\\reseller-edit-order-modal.component.ts", "D:\\connect\\connect_mobile\\src\\app\\reseller\\reseller-edit-order-modal\\reseller-edit-order-modal.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { FormArray, FormGroup, FormControl, Validators, AbstractControl, ReactiveFormsModule } from '@angular/forms';\nimport { ModalController, AlertController } from '@ionic/angular';\nimport { IonHeader, IonToolbar, IonTitle, IonButtons, IonButton, IonIcon, IonContent, IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonItem, IonLabel, IonInput, IonFooter, IonSpinner } from '@ionic/angular/standalone';\nimport { CommonModule } from '@angular/common';\nimport { Order } from 'app/connect_modules/orders-utility/types/order';\nimport { ResellerOrdersApiService } from 'app/connect_modules/reseller/services/reseller-orders-api.service';\nimport { WarehouseProductVariant } from 'app/connect_modules/shared/types/product-variant';\nimport { Store } from '@ngrx/store';\nimport { updateResellerOrderHttpFail } from 'app/connect_modules/ngrx-stores/reseller/store/actions/reseller.actions';\nimport { Util } from 'app/connect_modules/shared/utils/util';\nimport { UtilService } from 'app/shared/services/util.service';\n@Component({\n  selector: 'app-reseller-edit-order-modal',\n  templateUrl: './reseller-edit-order-modal.component.html',\n  styleUrls: ['./reseller-edit-order-modal.component.scss'],\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    IonHeader,\n    IonToolbar,\n    IonTitle,\n    IonButtons,\n    IonButton,\n    IonIcon,\n    IonContent,\n    IonCard,\n    IonCardHeader,\n    IonCardTitle,\n    IonCardContent,\n    IonItem,\n    IonLabel,\n    IonInput,\n    IonFooter,\n    IonSpinner\n  ],\n  providers: [ModalController, AlertController]\n})\nexport class ResellerEditOrderModalComponent implements OnInit {\n  @Input() orderData!: Order;\n\n  orderForm: FormGroup = new FormGroup({});\n  isSaving = false;\n  originalProductIds: number[] = [];\n\n  constructor(\n    private modalController: ModalController,\n    private resellerOrdersApiService: ResellerOrdersApiService,\n    private store: Store,\n    private alertController: AlertController,\n    private utilService: UtilService \n  ) {}\n\n  ngOnInit(): void {\n    this.initForm();\n    this.loadOrderData();\n    this.disableCalculatedFields();\n    this.storeOriginalProductIds();\n  }\n\n  initForm(): void {\n    this.orderForm = new FormGroup({\n      order_lines: new FormArray([])\n    });\n  }\n\n  private storeOriginalProductIds(): void {\n    if (this.orderData?.order_lines) {\n      this.originalProductIds = this.orderData.order_lines\n        .map(line => line.product_variant?.variant?.product?.id)\n        .filter(id => id) as number[];\n    }\n  }\n\n  loadOrderData(): void {\n    if (!this.orderData?.order_lines) return;\n    \n    const orderLinesArray = this.orderForm.get('order_lines') as FormArray;\n    this.orderData.order_lines.forEach(line => {\n      const lineGroup = new FormGroup({\n        id: new FormControl(line.id),\n        product_variant: new FormControl(line.product_variant, Validators.required),\n        quantity: new FormControl(line.quantity, [Validators.required, Validators.min(1)]),\n        price: new FormControl(line.price),\n        total_price: new FormControl(line.price * line.quantity),\n        variant_changed: new FormControl(false),\n        variant_change_reason: new FormControl('')\n      });\n      orderLinesArray.push(lineGroup);\n    });\n  }\n\n  get orderLines() {\n    return this.orderForm.get('order_lines') as FormArray;\n  }\n\n  searchWarehouseVariant = (searchTerm: string) => {\n    let params: any = {\n      filters: [{\n        \"operator\": \"or\",\n        \"filters\": [\n          {\"field\": \"variant__name\", \"operator\": \"contains\", \"value\": searchTerm},\n          {\"field\": \"variant__sku\", \"operator\": \"contains\", \"value\": searchTerm}\n        ]\n      }],\n    };\n    \n    this.addProductIdFilter(params);\n    \n    return this.resellerOrdersApiService.getResellerWarehouseVariants(params)\n      .toPromise()\n      .then((data) => data?.records || []);\n  }\n\n  private disableCalculatedFields() {\n    this.orderLines.controls.forEach(control => {\n      control.get('quantity')?.disable();\n      control.get('price')?.disable();\n      control.get('total_price')?.disable();\n    });\n  }\n\n  addProductIdFilter(params: any) {\n    if (this.originalProductIds.length > 0) {\n      const productFilter = [{\n        \"operator\": \"and\",\n        \"filters\": [{\"field\": \"variant__product__id\", \"operator\": \"in\", \"value\": this.originalProductIds.map(id => id.toString())}]\n      }];\n      params.filters = Util.concatFilters(params.filters ?? [], productFilter);\n    }\n  }\n\n  async selectProduct(orderLineControl: AbstractControl, index: number) {\n    try {\n      const searchTerm = '';\n      const variants = await this.searchWarehouseVariant(searchTerm);\n      \n      const inputs = variants.map(variant => ({\n        name: 'variant',\n        type: 'radio' as const,\n        label: variant.variant.name,\n        value: variant\n      }));\n\n      const alert = await this.alertController.create({\n        header: 'Select Product Variant',\n        inputs: inputs,\n        buttons: [\n          {\n            text: 'Cancel',\n            role: 'cancel'\n          },\n          {\n            text: 'OK',\n            handler: (selectedVariant: WarehouseProductVariant) => {\n              this.handleChangeProduct(selectedVariant, orderLineControl, index);\n            }\n          }\n        ]\n      });\n\n      await alert.present();\n    } catch (error) {\n      console.error('Error loading variants:', error);\n    }\n  }\n\n  handleChangeProduct(event: WarehouseProductVariant, orderLineControl: AbstractControl, i: number) {\n    if (event) {\n      const quantity = orderLineControl.get('quantity')?.value || 1;\n      \n      orderLineControl.patchValue({\n        product_variant: event,\n        price: event.variant.product.price,\n        total_price: event.variant.product.price * quantity,\n        variant_changed: true\n      });\n      \n      console.log('Product changed:', event, 'for line:', i);\n    }\n  }\n\n  async saveChanges() {\n  if (this.orderForm.valid) {\n    this.isSaving = true;\n    \n    const orderLines = this.orderForm.get('order_lines') as FormArray;\n    const updatedOrderLines = orderLines.getRawValue().map((line: any) => ({\n      id: line.id,\n      product_variant: typeof line.product_variant === 'object' ? line.product_variant.id : line.product_variant,\n      quantity: line.quantity,\n      price: line.price,\n      variant_changed: line.variant_changed || false,\n      variant_change_reason: line.variant_change_reason || ''\n    }));\n\n    const updateData = {\n      id: this.orderData.id,\n      order_lines: updatedOrderLines\n    };\n\n    console.log('Sending update data:', updateData);\n\n    try {\n      const response = await this.resellerOrdersApiService.updateResellerOrder(updateData).toPromise();\n      console.log('Update response:', response);\n      this.isSaving = false;\n      \n      // Show success/fail messages using toast\n      const responseData = response as any;\n      if (responseData?.success_messages?.length || responseData?.fail_messages?.length) {\n        // Show success messages\n        if (responseData.success_messages?.length) {\n          const successMsg = responseData.success_messages.join('\\n');\n          this.utilService.presentToast(successMsg, 'success', 3000, 'top', 'Order Updated');\n        }\n        \n        // Show fail messages\n        if (responseData.fail_messages?.length) {\n          const failMsg = responseData.fail_messages.join('\\n');\n          this.utilService.presentToast(failMsg, 'danger', 5000, 'top', 'Update Issues');\n        }\n      }\n      \n      this.modalController.dismiss(true);\n    } catch (error) {\n      console.error('Update error:', error);\n      this.isSaving = false;\n      this.utilService.presentToast('Failed to update order. Please try again.', 'danger', 3000, 'top', error.toString());\n      this.store.dispatch(updateResellerOrderHttpFail({ error }));\n    }\n  }\n}\n\n  closeModal() {\n    this.modalController.dismiss(false);\n  }\n\n  getProductVariantName(productVariant: any): string {\n    if (!productVariant) return 'Select Product';\n    if (typeof productVariant === 'string') return productVariant;\n    return productVariant?.variant?.name || productVariant?.name || 'Select Product';\n  }\n}", "<ion-header>\n  <ion-toolbar>\n    <ion-title>Edit Order</ion-title>\n    <ion-buttons slot=\"end\">\n      <ion-button (click)=\"closeModal()\">\n        <ion-icon name=\"close\"></ion-icon>\n      </ion-button>\n    </ion-buttons>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content>\n  <form [formGroup]=\"orderForm\">\n    <div formArrayName=\"order_lines\">\n      <ion-card *ngFor=\"let orderLineControl of orderLines.controls; let i = index\" [formGroupName]=\"i\">\n        <ion-card-header>\n          <ion-card-title>Product {{ i + 1 }}</ion-card-title>\n        </ion-card-header>\n        \n        <ion-card-content>\n          <ion-item>\n            <ion-label position=\"stacked\">Product Variant</ion-label>\n            <ion-input \n              formControlName=\"product_variant\" \n              readonly \n              [value]=\"getProductVariantName(orderLineControl.get('product_variant')?.value)\"\n              (click)=\"selectProduct(orderLineControl, i)\">\n            </ion-input>\n          </ion-item>\n\n          <ion-item>\n            <ion-label position=\"stacked\">Quantity</ion-label>\n            <ion-input formControlName=\"quantity\" type=\"number\" readonly></ion-input>\n          </ion-item>\n\n          <ion-item>\n            <ion-label position=\"stacked\">Price</ion-label>\n            <ion-input formControlName=\"price\" type=\"number\" readonly></ion-input>\n          </ion-item>\n\n          <ion-item>\n            <ion-label position=\"stacked\">Total Price</ion-label>\n            <ion-input formControlName=\"total_price\" type=\"number\" readonly></ion-input>\n          </ion-item>\n        </ion-card-content>\n      </ion-card>\n    </div>\n  </form>\n</ion-content>\n\n<ion-footer>\n  <ion-toolbar>\n    <ion-button \n      expand=\"block\" \n      (click)=\"saveChanges()\" \n      [disabled]=\"isSaving || !orderForm.valid\"\n      color=\"primary\">\n      <ion-spinner *ngIf=\"isSaving\" name=\"crescent\"></ion-spinner>\n      <span *ngIf=\"!isSaving\">Save Changes</span>\n    </ion-button>\n  </ion-toolbar>\n</ion-footer>"], "mappings": ";;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAmBC,mBAAmB,QAAQ,gBAAgB;AACpH,SAASC,eAAe,EAAEC,eAAe,QAAQ,gBAAgB;AACjE,SAASC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,aAAa,EAAEC,YAAY,EAAEC,cAAc,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,2BAA2B;AACjO,SAASC,YAAY,QAAQ,iBAAiB;AAK9C,SAASC,2BAA2B,QAAQ,yEAAyE;AACrH,SAASC,IAAI,QAAQ,uCAAuC;;;;;;;;;;;ICMlDC,EAFJ,CAAAC,cAAA,kBAAkG,sBAC/E,qBACC;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IACrCF,EADqC,CAAAG,YAAA,EAAiB,EACpC;IAIdH,EAFJ,CAAAC,cAAA,uBAAkB,eACN,oBACsB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzDH,EAAA,CAAAC,cAAA,oBAI+C;IAA7CD,EAAA,CAAAI,UAAA,mBAAAC,gFAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,mBAAA,GAAAH,MAAA,CAAAI,SAAA;MAAA,MAAAC,IAAA,GAAAL,MAAA,CAAAM,KAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAG,aAAA,CAAAP,mBAAA,EAAAE,IAAA,CAAkC;IAAA,EAAC;IAEhDX,EADE,CAAAG,YAAA,EAAY,EACH;IAGTH,EADF,CAAAC,cAAA,eAAU,qBACsB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAClDH,EAAA,CAAAiB,SAAA,qBAAyE;IAC3EjB,EAAA,CAAAG,YAAA,EAAW;IAGTH,EADF,CAAAC,cAAA,gBAAU,qBACsB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC/CH,EAAA,CAAAiB,SAAA,qBAAsE;IACxEjB,EAAA,CAAAG,YAAA,EAAW;IAGTH,EADF,CAAAC,cAAA,gBAAU,qBACsB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrDH,EAAA,CAAAiB,SAAA,qBAA4E;IAGlFjB,EAFI,CAAAG,YAAA,EAAW,EACM,EACV;;;;;;;IA/BmEH,EAAA,CAAAkB,UAAA,kBAAAP,IAAA,CAAmB;IAE7EX,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAoB,kBAAA,aAAAT,IAAA,SAAmB;IAS/BX,EAAA,CAAAmB,SAAA,GAA+E;IAA/EnB,EAAA,CAAAkB,UAAA,UAAAL,MAAA,CAAAQ,qBAAA,EAAAC,OAAA,GAAAb,mBAAA,CAAAc,GAAA,sCAAAD,OAAA,CAAAE,KAAA,EAA+E;;;;;IAgCvFxB,EAAA,CAAAiB,SAAA,sBAA4D;;;;;IAC5DjB,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADnBjD,OAAM,MAAOsB,+BAA+B;EAO1CC,YACUC,eAAgC,EAChCC,wBAAkD,EAClDC,KAAY,EACZC,eAAgC,EAChCC,WAAwB;IAJxB,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IATrB,KAAAC,SAAS,GAAc,IAAIzD,SAAS,CAAC,EAAE,CAAC;IACxC,KAAA0D,QAAQ,GAAG,KAAK;IAChB,KAAAC,kBAAkB,GAAa,EAAE;IAqDjC,KAAAC,sBAAsB,GAAIC,UAAkB,IAAI;MAC9C,IAAIC,MAAM,GAAQ;QAChBC,OAAO,EAAE,CAAC;UACR,UAAU,EAAE,IAAI;UAChB,SAAS,EAAE,CACT;YAAC,OAAO,EAAE,eAAe;YAAE,UAAU,EAAE,UAAU;YAAE,OAAO,EAAEF;UAAU,CAAC,EACvE;YAAC,OAAO,EAAE,cAAc;YAAE,UAAU,EAAE,UAAU;YAAE,OAAO,EAAEA;UAAU,CAAC;SAEzE;OACF;MAED,IAAI,CAACG,kBAAkB,CAACF,MAAM,CAAC;MAE/B,OAAO,IAAI,CAACT,wBAAwB,CAACY,4BAA4B,CAACH,MAAM,CAAC,CACtEI,SAAS,EAAE,CACXC,IAAI,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,OAAO,KAAI,EAAE,CAAC;IACxC,CAAC;EA7DE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAH,QAAQA,CAAA;IACN,IAAI,CAACd,SAAS,GAAG,IAAIzD,SAAS,CAAC;MAC7B2E,WAAW,EAAE,IAAI5E,SAAS,CAAC,EAAE;KAC9B,CAAC;EACJ;EAEQ2E,uBAAuBA,CAAA;IAAA,IAAAE,eAAA;IAC7B,KAAAA,eAAA,GAAI,IAAI,CAACC,SAAS,cAAAD,eAAA,eAAdA,eAAA,CAAgBD,WAAW,EAAE;MAC/B,IAAI,CAAChB,kBAAkB,GAAG,IAAI,CAACkB,SAAS,CAACF,WAAW,CACjDG,GAAG,CAACC,IAAI;QAAA,IAAAC,qBAAA;QAAA,QAAAA,qBAAA,GAAID,IAAI,CAACE,eAAe,cAAAD,qBAAA,gBAAAA,qBAAA,GAApBA,qBAAA,CAAsBE,OAAO,cAAAF,qBAAA,gBAAAA,qBAAA,GAA7BA,qBAAA,CAA+BG,OAAO,cAAAH,qBAAA,uBAAtCA,qBAAA,CAAwCI,EAAE;MAAA,EAAC,CACvDC,MAAM,CAACD,EAAE,IAAIA,EAAE,CAAa;IACjC;EACF;EAEAZ,aAAaA,CAAA;IAAA,IAAAc,gBAAA;IACX,IAAI,GAAAA,gBAAA,GAAC,IAAI,CAACT,SAAS,cAAAS,gBAAA,eAAdA,gBAAA,CAAgBX,WAAW,GAAE;IAElC,MAAMY,eAAe,GAAG,IAAI,CAAC9B,SAAS,CAACT,GAAG,CAAC,aAAa,CAAc;IACtE,IAAI,CAAC6B,SAAS,CAACF,WAAW,CAACa,OAAO,CAACT,IAAI,IAAG;MACxC,MAAMU,SAAS,GAAG,IAAIzF,SAAS,CAAC;QAC9BoF,EAAE,EAAE,IAAInF,WAAW,CAAC8E,IAAI,CAACK,EAAE,CAAC;QAC5BH,eAAe,EAAE,IAAIhF,WAAW,CAAC8E,IAAI,CAACE,eAAe,EAAE/E,UAAU,CAACwF,QAAQ,CAAC;QAC3EC,QAAQ,EAAE,IAAI1F,WAAW,CAAC8E,IAAI,CAACY,QAAQ,EAAE,CAACzF,UAAU,CAACwF,QAAQ,EAAExF,UAAU,CAAC0F,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClFC,KAAK,EAAE,IAAI5F,WAAW,CAAC8E,IAAI,CAACc,KAAK,CAAC;QAClCC,WAAW,EAAE,IAAI7F,WAAW,CAAC8E,IAAI,CAACc,KAAK,GAAGd,IAAI,CAACY,QAAQ,CAAC;QACxDI,eAAe,EAAE,IAAI9F,WAAW,CAAC,KAAK,CAAC;QACvC+F,qBAAqB,EAAE,IAAI/F,WAAW,CAAC,EAAE;OAC1C,CAAC;MACFsF,eAAe,CAACU,IAAI,CAACR,SAAS,CAAC;IACjC,CAAC,CAAC;EACJ;EAEA,IAAIS,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACzC,SAAS,CAACT,GAAG,CAAC,aAAa,CAAc;EACvD;EAoBQyB,uBAAuBA,CAAA;IAC7B,IAAI,CAACyB,UAAU,CAACC,QAAQ,CAACX,OAAO,CAACY,OAAO,IAAG;MAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;MACzC,CAAAF,YAAA,GAAAD,OAAO,CAACpD,GAAG,CAAC,UAAU,CAAC,cAAAqD,YAAA,eAAvBA,YAAA,CAAyBG,OAAO,EAAE;MAClC,CAAAF,aAAA,GAAAF,OAAO,CAACpD,GAAG,CAAC,OAAO,CAAC,cAAAsD,aAAA,eAApBA,aAAA,CAAsBE,OAAO,EAAE;MAC/B,CAAAD,aAAA,GAAAH,OAAO,CAACpD,GAAG,CAAC,aAAa,CAAC,cAAAuD,aAAA,eAA1BA,aAAA,CAA4BC,OAAO,EAAE;IACvC,CAAC,CAAC;EACJ;EAEAxC,kBAAkBA,CAACF,MAAW;IAC5B,IAAI,IAAI,CAACH,kBAAkB,CAAC8C,MAAM,GAAG,CAAC,EAAE;MAAA,IAAAC,eAAA;MACtC,MAAMC,aAAa,GAAG,CAAC;QACrB,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,CAAC;UAAC,OAAO,EAAE,sBAAsB;UAAE,UAAU,EAAE,IAAI;UAAE,OAAO,EAAE,IAAI,CAAChD,kBAAkB,CAACmB,GAAG,CAACM,EAAE,IAAIA,EAAE,CAACwB,QAAQ,EAAE;QAAC,CAAC;OAC3H,CAAC;MACF9C,MAAM,CAACC,OAAO,GAAGvC,IAAI,CAACqF,aAAa,EAAAH,eAAA,GAAC5C,MAAM,CAACC,OAAO,cAAA2C,eAAA,cAAAA,eAAA,GAAI,EAAE,EAAEC,aAAa,CAAC;IAC1E;EACF;EAEMlE,aAAaA,CAACqE,gBAAiC,EAAEzE,KAAa;IAAA,IAAA0E,KAAA;IAAA,OAAAC,iBAAA;MAClE,IAAI;QACF,MAAMnD,UAAU,GAAG,EAAE;QACrB,MAAMoD,QAAQ,SAASF,KAAI,CAACnD,sBAAsB,CAACC,UAAU,CAAC;QAE9D,MAAMqD,MAAM,GAAGD,QAAQ,CAACnC,GAAG,CAACI,OAAO,KAAK;UACtCiC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,OAAgB;UACtBC,KAAK,EAAEnC,OAAO,CAACA,OAAO,CAACiC,IAAI;UAC3BlE,KAAK,EAAEiC;SACR,CAAC,CAAC;QAEH,MAAMoC,KAAK,SAASP,KAAI,CAACxD,eAAe,CAACgE,MAAM,CAAC;UAC9CC,MAAM,EAAE,wBAAwB;UAChCN,MAAM,EAAEA,MAAM;UACdO,OAAO,EAAE,CACP;YACEC,IAAI,EAAE,QAAQ;YACdC,IAAI,EAAE;WACP,EACD;YACED,IAAI,EAAE,IAAI;YACVE,OAAO,EAAGC,eAAwC,IAAI;cACpDd,KAAI,CAACe,mBAAmB,CAACD,eAAe,EAAEf,gBAAgB,EAAEzE,KAAK,CAAC;YACpE;WACD;SAEJ,CAAC;QAEF,MAAMiF,KAAK,CAACS,OAAO,EAAE;MACvB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IAAC;EACH;EAEAF,mBAAmBA,CAACI,KAA8B,EAAEpB,gBAAiC,EAAEqB,CAAS;IAC9F,IAAID,KAAK,EAAE;MAAA,IAAAE,qBAAA;MACT,MAAMzC,QAAQ,GAAG,EAAAyC,qBAAA,GAAAtB,gBAAgB,CAAC9D,GAAG,CAAC,UAAU,CAAC,cAAAoF,qBAAA,uBAAhCA,qBAAA,CAAkCnF,KAAK,KAAI,CAAC;MAE7D6D,gBAAgB,CAACuB,UAAU,CAAC;QAC1BpD,eAAe,EAAEiD,KAAK;QACtBrC,KAAK,EAAEqC,KAAK,CAAChD,OAAO,CAACC,OAAO,CAACU,KAAK;QAClCC,WAAW,EAAEoC,KAAK,CAAChD,OAAO,CAACC,OAAO,CAACU,KAAK,GAAGF,QAAQ;QACnDI,eAAe,EAAE;OAClB,CAAC;MAEFkC,OAAO,CAACK,GAAG,CAAC,kBAAkB,EAAEJ,KAAK,EAAE,WAAW,EAAEC,CAAC,CAAC;IACxD;EACF;EAEMI,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAxB,iBAAA;MACjB,IAAIwB,MAAI,CAAC/E,SAAS,CAACgF,KAAK,EAAE;QACxBD,MAAI,CAAC9E,QAAQ,GAAG,IAAI;QAEpB,MAAMwC,UAAU,GAAGsC,MAAI,CAAC/E,SAAS,CAACT,GAAG,CAAC,aAAa,CAAc;QACjE,MAAM0F,iBAAiB,GAAGxC,UAAU,CAACyC,WAAW,EAAE,CAAC7D,GAAG,CAAEC,IAAS,KAAM;UACrEK,EAAE,EAAEL,IAAI,CAACK,EAAE;UACXH,eAAe,EAAE,OAAOF,IAAI,CAACE,eAAe,KAAK,QAAQ,GAAGF,IAAI,CAACE,eAAe,CAACG,EAAE,GAAGL,IAAI,CAACE,eAAe;UAC1GU,QAAQ,EAAEZ,IAAI,CAACY,QAAQ;UACvBE,KAAK,EAAEd,IAAI,CAACc,KAAK;UACjBE,eAAe,EAAEhB,IAAI,CAACgB,eAAe,IAAI,KAAK;UAC9CC,qBAAqB,EAAEjB,IAAI,CAACiB,qBAAqB,IAAI;SACtD,CAAC,CAAC;QAEH,MAAM4C,UAAU,GAAG;UACjBxD,EAAE,EAAEoD,MAAI,CAAC3D,SAAS,CAACO,EAAE;UACrBT,WAAW,EAAE+D;SACd;QAEDT,OAAO,CAACK,GAAG,CAAC,sBAAsB,EAAEM,UAAU,CAAC;QAE/C,IAAI;UAAA,IAAAC,qBAAA,EAAAC,qBAAA;UACF,MAAMC,QAAQ,SAASP,MAAI,CAACnF,wBAAwB,CAAC2F,mBAAmB,CAACJ,UAAU,CAAC,CAAC1E,SAAS,EAAE;UAChG+D,OAAO,CAACK,GAAG,CAAC,kBAAkB,EAAES,QAAQ,CAAC;UACzCP,MAAI,CAAC9E,QAAQ,GAAG,KAAK;UAErB;UACA,MAAMuF,YAAY,GAAGF,QAAe;UACpC,IAAIE,YAAY,aAAZA,YAAY,gBAAAJ,qBAAA,GAAZI,YAAY,CAAEC,gBAAgB,cAAAL,qBAAA,eAA9BA,qBAAA,CAAgCpC,MAAM,IAAIwC,YAAY,aAAZA,YAAY,gBAAAH,qBAAA,GAAZG,YAAY,CAAEE,aAAa,cAAAL,qBAAA,eAA3BA,qBAAA,CAA6BrC,MAAM,EAAE;YAAA,IAAA2C,sBAAA,EAAAC,sBAAA;YACjF;YACA,KAAAD,sBAAA,GAAIH,YAAY,CAACC,gBAAgB,cAAAE,sBAAA,eAA7BA,sBAAA,CAA+B3C,MAAM,EAAE;cACzC,MAAM6C,UAAU,GAAGL,YAAY,CAACC,gBAAgB,CAACK,IAAI,CAAC,IAAI,CAAC;cAC3Df,MAAI,CAAChF,WAAW,CAACgG,YAAY,CAACF,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC;YACpF;YAEA;YACA,KAAAD,sBAAA,GAAIJ,YAAY,CAACE,aAAa,cAAAE,sBAAA,eAA1BA,sBAAA,CAA4B5C,MAAM,EAAE;cACtC,MAAMgD,OAAO,GAAGR,YAAY,CAACE,aAAa,CAACI,IAAI,CAAC,IAAI,CAAC;cACrDf,MAAI,CAAChF,WAAW,CAACgG,YAAY,CAACC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC;YAChF;UACF;UAEAjB,MAAI,CAACpF,eAAe,CAACsG,OAAO,CAAC,IAAI,CAAC;QACpC,CAAC,CAAC,OAAO1B,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACrCQ,MAAI,CAAC9E,QAAQ,GAAG,KAAK;UACrB8E,MAAI,CAAChF,WAAW,CAACgG,YAAY,CAAC,2CAA2C,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAExB,KAAK,CAACpB,QAAQ,EAAE,CAAC;UACnH4B,MAAI,CAAClF,KAAK,CAACqG,QAAQ,CAACpI,2BAA2B,CAAC;YAAEyG;UAAK,CAAE,CAAC,CAAC;QAC7D;MACF;IAAC;EACH;EAEE4B,UAAUA,CAAA;IACR,IAAI,CAACxG,eAAe,CAACsG,OAAO,CAAC,KAAK,CAAC;EACrC;EAEA5G,qBAAqBA,CAAC+G,cAAmB;IAAA,IAAAC,qBAAA;IACvC,IAAI,CAACD,cAAc,EAAE,OAAO,gBAAgB;IAC5C,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE,OAAOA,cAAc;IAC7D,OAAO,CAAAA,cAAc,aAAdA,cAAc,gBAAAC,qBAAA,GAAdD,cAAc,CAAE3E,OAAO,cAAA4E,qBAAA,uBAAvBA,qBAAA,CAAyB3C,IAAI,MAAI0C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE1C,IAAI,KAAI,gBAAgB;EAClF;;mCA5MWjE,+BAA+B;;mCAA/BA,gCAA+B,EAAAzB,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAA5J,eAAA,GAAAqB,EAAA,CAAAsI,iBAAA,CAAAE,EAAA,CAAAC,wBAAA,GAAAzI,EAAA,CAAAsI,iBAAA,CAAAI,EAAA,CAAAC,KAAA,GAAA3I,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAA3J,eAAA,GAAAoB,EAAA,CAAAsI,iBAAA,CAAAM,EAAA,CAAAC,WAAA;AAAA;;QAA/BpH,gCAA+B;EAAAqH,SAAA;EAAArD,MAAA;IAAArC,SAAA;EAAA;EAAA2F,UAAA;EAAAC,QAAA,GAAAhJ,EAAA,CAAAiJ,kBAAA,CAF/B,CAACtK,eAAe,EAAEC,eAAe,CAAC,GAAAoB,EAAA,CAAAkJ,mBAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCnC3CxJ,EAFJ,CAAAC,cAAA,iBAAY,kBACG,gBACA;MAAAD,EAAA,CAAAE,MAAA,iBAAU;MAAAF,EAAA,CAAAG,YAAA,EAAY;MAE/BH,EADF,CAAAC,cAAA,qBAAwB,oBACa;MAAvBD,EAAA,CAAAI,UAAA,mBAAAsJ,qEAAA;QAAA,OAASD,GAAA,CAAAtB,UAAA,EAAY;MAAA,EAAC;MAChCnI,EAAA,CAAAiB,SAAA,kBAAkC;MAI1CjB,EAHM,CAAAG,YAAA,EAAa,EACD,EACF,EACH;MAITH,EAFJ,CAAAC,cAAA,kBAAa,cACmB,aACK;MAC/BD,EAAA,CAAA2J,UAAA,KAAAC,oDAAA,uBAAkG;MAkCxG5J,EAFI,CAAAG,YAAA,EAAM,EACD,EACK;MAIVH,EAFJ,CAAAC,cAAA,kBAAY,mBACG,qBAKO;MAFhBD,EAAA,CAAAI,UAAA,mBAAAyJ,sEAAA;QAAA,OAASJ,GAAA,CAAA3C,WAAA,EAAa;MAAA,EAAC;MAIvB9G,EADA,CAAA2J,UAAA,KAAAG,uDAAA,yBAA8C,KAAAC,gDAAA,kBACtB;MAG9B/J,EAFI,CAAAG,YAAA,EAAa,EACD,EACH;;;MAjDLH,EAAA,CAAAmB,SAAA,GAAuB;MAAvBnB,EAAA,CAAAkB,UAAA,cAAAuI,GAAA,CAAAzH,SAAA,CAAuB;MAEchC,EAAA,CAAAmB,SAAA,GAAwB;MAAxBnB,EAAA,CAAAkB,UAAA,YAAAuI,GAAA,CAAAhF,UAAA,CAAAC,QAAA,CAAwB;MAyC/D1E,EAAA,CAAAmB,SAAA,GAAyC;MAAzCnB,EAAA,CAAAkB,UAAA,aAAAuI,GAAA,CAAAxH,QAAA,KAAAwH,GAAA,CAAAzH,SAAA,CAAAgF,KAAA,CAAyC;MAE3BhH,EAAA,CAAAmB,SAAA,EAAc;MAAdnB,EAAA,CAAAkB,UAAA,SAAAuI,GAAA,CAAAxH,QAAA,CAAc;MACrBjC,EAAA,CAAAmB,SAAA,EAAe;MAAfnB,EAAA,CAAAkB,UAAA,UAAAuI,GAAA,CAAAxH,QAAA,CAAe;;;iBDxCxBpC,YAAY,EAAAmK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZxL,mBAAmB,EAAAyL,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,aAAA,EAAAN,EAAA,CAAAO,aAAA,EACnB7L,SAAS,EACTC,UAAU,EACVC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,aAAa,EACbC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,UAAU;EAAA+K,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}