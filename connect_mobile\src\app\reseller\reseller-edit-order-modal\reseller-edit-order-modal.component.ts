import { Component, Input, OnInit } from '@angular/core';
import { FormArray, FormGroup, FormControl, Validators, AbstractControl, ReactiveFormsModule } from '@angular/forms';
import { ModalController, AlertController } from '@ionic/angular';
import { IonHeader, IonToolbar, IonTitle, IonButtons, IonButton, IonIcon, IonContent, IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonItem, IonLabel, IonInput, IonFooter, IonSpinner } from '@ionic/angular/standalone';
import { CommonModule } from '@angular/common';
import { Order } from 'app/connect_modules/orders-utility/types/order';
import { ResellerOrdersApiService } from 'app/connect_modules/reseller/services/reseller-orders-api.service';
import { WarehouseProductVariant } from 'app/connect_modules/shared/types/product-variant';
import { Store } from '@ngrx/store';
import { updateResellerOrderHttpFail } from 'app/connect_modules/ngrx-stores/reseller/store/actions/reseller.actions';
import { Util } from 'app/connect_modules/shared/utils/util';
import { UtilService } from 'app/shared/services/util.service';
@Component({
  selector: 'app-reseller-edit-order-modal',
  templateUrl: './reseller-edit-order-modal.component.html',
  styleUrls: ['./reseller-edit-order-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonIcon,
    IonContent,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonItem,
    IonLabel,
    IonInput,
    IonFooter,
    IonSpinner
  ],
  providers: [ModalController, AlertController]
})
export class ResellerEditOrderModalComponent implements OnInit {
  @Input() orderData!: Order;

  orderForm: FormGroup = new FormGroup({});
  isSaving = false;
  originalProductIds: number[] = [];

  constructor(
    private modalController: ModalController,
    private resellerOrdersApiService: ResellerOrdersApiService,
    private store: Store,
    private alertController: AlertController,
    private utilService: UtilService 
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadOrderData();
    this.disableCalculatedFields();
    this.storeOriginalProductIds();
  }

  initForm(): void {
    this.orderForm = new FormGroup({
      order_lines: new FormArray([])
    });
  }

  private storeOriginalProductIds(): void {
    if (this.orderData?.order_lines) {
      this.originalProductIds = this.orderData.order_lines
        .map(line => line.product_variant?.variant?.product?.id)
        .filter(id => id) as number[];
    }
  }

  loadOrderData(): void {
    if (!this.orderData?.order_lines) return;
    
    const orderLinesArray = this.orderForm.get('order_lines') as FormArray;
    this.orderData.order_lines.forEach(line => {
      const lineGroup = new FormGroup({
        id: new FormControl(line.id),
        product_variant: new FormControl(line.product_variant, Validators.required),
        quantity: new FormControl(line.quantity, [Validators.required, Validators.min(1)]),
        price: new FormControl(line.price),
        total_price: new FormControl(line.price * line.quantity),
        variant_changed: new FormControl(false),
        variant_change_reason: new FormControl('')
      });
      orderLinesArray.push(lineGroup);
    });
  }

  get orderLines() {
    return this.orderForm.get('order_lines') as FormArray;
  }

  searchWarehouseVariant = (searchTerm: string) => {
    let params: any = {
      filters: [{
        "operator": "or",
        "filters": [
          {"field": "variant__name", "operator": "contains", "value": searchTerm},
          {"field": "variant__sku", "operator": "contains", "value": searchTerm}
        ]
      }],
    };
    
    this.addProductIdFilter(params);
    
    return this.resellerOrdersApiService.getResellerWarehouseVariants(params)
      .toPromise()
      .then((data) => data?.records || []);
  }

  private disableCalculatedFields() {
    this.orderLines.controls.forEach(control => {
      control.get('quantity')?.disable();
      control.get('price')?.disable();
      control.get('total_price')?.disable();
    });
  }

  addProductIdFilter(params: any) {
    if (this.originalProductIds.length > 0) {
      const productFilter = [{
        "operator": "and",
        "filters": [{"field": "variant__product__id", "operator": "in", "value": this.originalProductIds.map(id => id.toString())}]
      }];
      params.filters = Util.concatFilters(params.filters ?? [], productFilter);
    }
  }

  async selectProduct(orderLineControl: AbstractControl, index: number) {
    try {
      const searchTerm = '';
      const variants = await this.searchWarehouseVariant(searchTerm);
      
      const inputs = variants.map(variant => ({
        name: 'variant',
        type: 'radio' as const,
        label: variant.variant.name,
        value: variant
      }));

      const alert = await this.alertController.create({
        header: 'Select Product Variant',
        inputs: inputs,
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel'
          },
          {
            text: 'OK',
            handler: (selectedVariant: WarehouseProductVariant) => {
              this.handleChangeProduct(selectedVariant, orderLineControl, index);
            }
          }
        ]
      });

      await alert.present();
    } catch (error) {
      console.error('Error loading variants:', error);
    }
  }

  handleChangeProduct(event: WarehouseProductVariant, orderLineControl: AbstractControl, i: number) {
    if (event) {
      const quantity = orderLineControl.get('quantity')?.value || 1;
      
      orderLineControl.patchValue({
        product_variant: event,
        price: event.variant.product.price,
        total_price: event.variant.product.price * quantity,
        variant_changed: true
      });
      
      console.log('Product changed:', event, 'for line:', i);
    }
  }

  async saveChanges() {
  if (this.orderForm.valid) {
    this.isSaving = true;
    
    const orderLines = this.orderForm.get('order_lines') as FormArray;
    const updatedOrderLines = orderLines.getRawValue().map((line: any) => ({
      id: line.id,
      product_variant: typeof line.product_variant === 'object' ? line.product_variant.id : line.product_variant,
      quantity: line.quantity,
      price: line.price,
      variant_changed: line.variant_changed || false,
      variant_change_reason: line.variant_change_reason || ''
    }));

    const updateData = {
      id: this.orderData.id,
      order_lines: updatedOrderLines
    };

    console.log('Sending update data:', updateData);

    try {
      const response = await this.resellerOrdersApiService.updateResellerOrder(updateData).toPromise();
      console.log('Update response:', response);
      this.isSaving = false;
      
      // Show success/fail messages using toast
      const responseData = response as any;
      if (responseData?.success_messages?.length || responseData?.fail_messages?.length) {
        // Show success messages
        if (responseData.success_messages?.length) {
          const successMsg = responseData.success_messages.join('\n');
          this.utilService.presentToast(successMsg, 'success', 3000, 'top', 'Order Updated');
        }
        
        // Show fail messages
        if (responseData.fail_messages?.length) {
          const failMsg = responseData.fail_messages.join('\n');
          this.utilService.presentToast(failMsg, 'danger', 5000, 'top', 'Update Issues');
        }
      }
      
      this.modalController.dismiss(true);
    } catch (error) {
      console.error('Update error:', error);
      this.isSaving = false;
      this.utilService.presentToast('Failed to update order. Please try again.', 'danger', 3000, 'top', "Failed to update product in order");
      this.store.dispatch(updateResellerOrderHttpFail({ error }));
    }
  }
}

  closeModal() {
    this.modalController.dismiss(false);
  }

  getProductVariantName(productVariant: any): string {
    if (!productVariant) return 'Select Product';
    if (typeof productVariant === 'string') return productVariant;

    // Handle WarehouseProductVariant structure: productVariant.variant.name
    if (productVariant?.variant?.name) {
      return productVariant.variant.name;
    }

    // Fallback to direct name property
    if (productVariant?.name) {
      return productVariant.name;
    }

    // Debug log for troubleshooting
    console.log('Product variant structure:', productVariant);

    return 'Select Product';
  }
}